import { Connection, PublicKey, clusterApiUrl } from '@solana/web3.js'

// Solana network configuration
export const SOLANA_NETWORK = process.env.SOLANA_NETWORK || 'devnet'
export const SOLANA_RPC_URL = process.env.SOLANA_RPC_URL || clusterApiUrl(SOLANA_NETWORK as any)

// Admin wallet configuration (where subscription payments go)
export const ADMIN_WALLET_ADDRESS = process.env.ADMIN_WALLET_ADDRESS || ''

// Subscription settings
export const SUBSCRIPTION_PRICE_SOL = parseFloat(process.env.SUBSCRIPTION_PRICE_SOL || '2')
export const SUBSCRIPTION_DURATION_DAYS = parseInt(process.env.SUBSCRIPTION_DURATION_DAYS || '30')

// Create Solana connection
export const connection = new Connection(SOLANA_RPC_URL, 'confirmed')

// Convert SOL to lamports (1 SOL = 1,000,000,000 lamports)
export const SUBSCRIPTION_PRICE_LAMPORTS = SUBSCRIPTION_PRICE_SOL * 1_000_000_000

// Validate admin wallet address
export const getAdminWalletPublicKey = (): PublicKey | null => {
  try {
    if (!ADMIN_WALLET_ADDRESS) {
      console.warn('Admin wallet address not configured')
      return null
    }
    return new PublicKey(ADMIN_WALLET_ADDRESS)
  } catch (error) {
    console.error('Invalid admin wallet address:', error)
    return null
  }
}

// Network configuration for wallet adapters
export const WALLET_ADAPTER_NETWORK = SOLANA_NETWORK as any

// RPC endpoint for wallet adapters
export const WALLET_ADAPTER_RPC_ENDPOINT = SOLANA_RPC_URL
