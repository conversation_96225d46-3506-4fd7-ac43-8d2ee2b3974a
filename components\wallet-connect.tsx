"use client"

import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useWallet } from "@/lib/wallet-context"
import { CheckCircle, Loader2, Wallet } from "lucide-react"
import { useState } from "react"

export default function WalletConnect() {
  const { wallet, connecting, connectWallet, disconnectWallet } = useWallet()
  const [copied, setCopied] = useState(false)

  const copyAddress = () => {
    if (wallet.publicKey) {
      navigator.clipboard.writeText(wallet.publicKey)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }

  const formatAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`
  }

  if (wallet.connected) {
    return (
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2 text-sm">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <span className="text-green-600 font-medium">Connected</span>
        </div>
        <div className="text-sm text-gray-600">
          {wallet.balance !== null ? `${wallet.balance.toFixed(4)} SOL` : 'Loading...'}
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="flex items-center gap-2">
              <Wallet className="h-4 w-4" />
              <span>{formatAddress(wallet.publicKey!)}</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={copyAddress}>{copied ? "Copied!" : "Copy Address"}</DropdownMenuItem>
            <DropdownMenuItem onClick={disconnectWallet}>Disconnect</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    )
  }

  return (
    <div className="flex items-center gap-2">
      <Button onClick={connectWallet} disabled={connecting} className="bg-purple-600 hover:bg-purple-700">
        {connecting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Connecting...
          </>
        ) : (
          <>
            <Wallet className="mr-2 h-4 w-4" />
            Connect Wallet
          </>
        )}
      </Button>
    </div>
  )
}
