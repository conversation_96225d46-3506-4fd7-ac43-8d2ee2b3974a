"use client"

import { createContext, useContext, useEffect, useState, type ReactNode } from "react"
import { getWalletBalance } from './solana-utils'

// Define the types for our wallet connection
type Wallet = {
  publicKey: string | null
  connected: boolean
  balance: number | null
}

type WalletContextType = {
  wallet: Wallet
  connecting: boolean
  connectWallet: () => Promise<void>
  disconnectWallet: () => void
  refreshBalance: () => Promise<void>
}

// Create the context with default values
const WalletContext = createContext<WalletContextType>({
  wallet: { publicKey: null, connected: false, balance: null },
  connecting: false,
  connectWallet: async () => {},
  disconnectWallet: () => {},
  refreshBalance: async () => {},
})

// Custom hook to use the wallet context
export const useWallet = () => useContext(WalletContext)

// Provider component
export function WalletProvider({ children }: { children: ReactNode }) {
  const [wallet, setWallet] = useState<Wallet>({
    publicKey: null,
    connected: false,
    balance: null,
  })
  const [connecting, setConnecting] = useState(false)

  // Check if wallet is already connected on component mount
  useEffect(() => {
    const checkWalletConnection = async () => {
      if (typeof window !== "undefined" && window.solana) {
        try {
          // Check if the wallet is already connected
          const response = await window.solana.connect({ onlyIfTrusted: true })
          const balance = await getWalletBalance(response.publicKey.toString())
          setWallet({
            publicKey: response.publicKey.toString(),
            connected: true,
            balance,
          })
        } catch (error) {
          // Wallet not connected or not trusted
          console.log("Wallet not connected:", error)
        }
      }
    }

    checkWalletConnection()
  }, [])

  // Refresh balance function
  const refreshBalance = async () => {
    if (wallet.publicKey) {
      try {
        const balance = await getWalletBalance(wallet.publicKey)
        setWallet(prev => ({ ...prev, balance }))
      } catch (error) {
        console.error("Error refreshing balance:", error)
      }
    }
  }

  // Connect wallet function
  const connectWallet = async () => {
    if (typeof window !== "undefined" && window.solana) {
      try {
        setConnecting(true)
        const response = await window.solana.connect()
        const balance = await getWalletBalance(response.publicKey.toString())

        setWallet({
          publicKey: response.publicKey.toString(),
          connected: true,
          balance,
        })
      } catch (error) {
        console.error("Error connecting wallet:", error)
      } finally {
        setConnecting(false)
      }
    } else {
      window.open("https://phantom.app/", "_blank")
    }
  }

  // Disconnect wallet function
  const disconnectWallet = () => {
    if (typeof window !== "undefined" && window.solana) {
      window.solana.disconnect()
      setWallet({
        publicKey: null,
        connected: false,
        balance: null,
      })
    }
  }

  return (
    <WalletContext.Provider value={{ wallet, connecting, connectWallet, disconnectWallet, refreshBalance }}>
      {children}
    </WalletContext.Provider>
  )
}

// Add this to global.d.ts or a similar type definition file
declare global {
  interface Window {
    solana?: {
      connect: (options?: { onlyIfTrusted?: boolean }) => Promise<{ publicKey: { toString: () => string } }>
      disconnect: () => Promise<void>
      signTransaction: (transaction: any) => Promise<any>
      signAllTransactions: (transactions: any[]) => Promise<any[]>
    }
  }
}
